import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy.stats import pearsonr


def calculate_pairwise_correlations(df, method='pearson', display_results=True, save_to_file=None):
    """
    Calculate pairwise correlations for all numeric columns in a DataFrame.
    
    Parameters:
    -----------
    df : pandas.DataFrame
        Input DataFrame with numeric columns
    method : str, default 'pearson'
        Correlation method ('pearson', 'spearman', 'kendall')
    display_results : bool, default True
        Whether to display correlation matrix and heatmap
    save_to_file : str, optional
        If provided, save correlation matrix to CSV file
    
    Returns:
    --------
    correlation_matrix : pandas.DataFrame
        Correlation matrix
    correlation_pairs : pandas.DataFrame
        Detailed pairwise correlations with p-values (for Pearson only)
    """
    
    # Calculate correlation matrix
    correlation_matrix = df.corr(method=method)
    
    # For detailed analysis with p-values (Pearson only)
    correlation_pairs = None
    if method == 'pearson':
        correlation_pairs = get_detailed_correlations(df)
    
    if display_results:
        display_correlation_results(correlation_matrix, df.name if hasattr(df, 'name') else 'Dataset')
    
    if save_to_file:
        correlation_matrix.to_csv(save_to_file)
        print(f"Correlation matrix saved to {save_to_file}")
    
    return correlation_matrix, correlation_pairs


def get_detailed_correlations(df):
    """
    Get detailed pairwise correlations with correlation coefficients and p-values.
    
    Parameters:
    -----------
    df : pandas.DataFrame
        Input DataFrame with numeric columns
    
    Returns:
    --------
    pandas.DataFrame
        DataFrame with columns: Variable1, Variable2, Correlation, P_value, Significance
    """
    
    columns = df.select_dtypes(include=[np.number]).columns
    results = []
    
    for i, col1 in enumerate(columns):
        for j, col2 in enumerate(columns):
            if i < j:  # Only calculate upper triangle to avoid duplicates
                # Remove NaN values for correlation calculation
                data1 = df[col1].dropna()
                data2 = df[col2].dropna()
                
                # Find common indices
                common_idx = data1.index.intersection(data2.index)
                if len(common_idx) > 1:
                    corr_coef, p_value = pearsonr(data1[common_idx], data2[common_idx])
                    
                    # Determine significance level
                    if p_value < 0.001:
                        significance = '***'
                    elif p_value < 0.01:
                        significance = '**'
                    elif p_value < 0.05:
                        significance = '*'
                    else:
                        significance = 'ns'
                    
                    results.append({
                        'Variable1': col1,
                        'Variable2': col2,
                        'Correlation': round(corr_coef, 4),
                        'P_value': round(p_value, 6),
                        'Significance': significance,
                        'Sample_size': len(common_idx)
                    })
    
    return pd.DataFrame(results).sort_values('Correlation', key=abs, ascending=False)


def display_correlation_results(correlation_matrix, dataset_name='Dataset'):
    """
    Display correlation matrix as heatmap and summary statistics.
    
    Parameters:
    -----------
    correlation_matrix : pandas.DataFrame
        Correlation matrix
    dataset_name : str
        Name of the dataset for plot titles
    """
    
    # Create figure with subplots
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
    
    # Heatmap with correlation values
    mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))
    sns.heatmap(correlation_matrix, 
                mask=mask,
                annot=True, 
                cmap='RdBu_r', 
                center=0,
                square=True,
                fmt='.3f',
                cbar_kws={'shrink': 0.8},
                ax=ax1)
    ax1.set_title(f'Correlation Matrix - {dataset_name}')
    ax1.set_xlabel('Body Parts')
    ax1.set_ylabel('Body Parts')
    
    # Correlation distribution
    # Get upper triangle values (excluding diagonal)
    upper_triangle = correlation_matrix.where(np.triu(np.ones(correlation_matrix.shape), k=1).astype(bool))
    correlations = upper_triangle.stack().values
    
    ax2.hist(correlations, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
    ax2.axvline(correlations.mean(), color='red', linestyle='--', 
                label=f'Mean: {correlations.mean():.3f}')
    ax2.set_xlabel('Correlation Coefficient')
    ax2.set_ylabel('Frequency')
    ax2.set_title(f'Distribution of Correlations - {dataset_name}')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    # Print summary statistics
    print(f"\n{'='*60}")
    print(f"CORRELATION SUMMARY - {dataset_name.upper()}")
    print(f"{'='*60}")
    print(f"Number of variables: {len(correlation_matrix.columns)}")
    print(f"Number of correlations: {len(correlations)}")
    print(f"Mean correlation: {correlations.mean():.4f}")
    print(f"Std correlation: {correlations.std():.4f}")
    print(f"Min correlation: {correlations.min():.4f}")
    print(f"Max correlation: {correlations.max():.4f}")
    
    # Find strongest correlations
    print(f"\nSTRONGEST POSITIVE CORRELATIONS:")
    print(f"{'-'*40}")
    strongest_positive = np.unravel_index(np.argmax(upper_triangle.values), upper_triangle.shape)
    strongest_pos_val = correlation_matrix.iloc[strongest_positive]
    print(f"{correlation_matrix.index[strongest_positive[0]]} - {correlation_matrix.columns[strongest_positive[1]]}: {strongest_pos_val:.4f}")
    
    print(f"\nSTRONGEST NEGATIVE CORRELATIONS:")
    print(f"{'-'*40}")
    strongest_negative = np.unravel_index(np.argmin(upper_triangle.values), upper_triangle.shape)
    strongest_neg_val = correlation_matrix.iloc[strongest_negative]
    print(f"{correlation_matrix.index[strongest_negative[0]]} - {correlation_matrix.columns[strongest_negative[1]]}: {strongest_neg_val:.4f}")


def analyze_calves_3x3_correlations(df_path=None, df=None):
    """
    Specific function to analyze correlations in the calves_3x3 dataset.
    
    Parameters:
    -----------
    df_path : str, optional
        Path to CSV file containing the data
    df : pandas.DataFrame, optional
        DataFrame containing calves_3x3 data
    
    Returns:
    --------
    correlation_matrix : pandas.DataFrame
        Correlation matrix
    correlation_pairs : pandas.DataFrame
        Detailed pairwise correlations
    """
    
    if df is None and df_path is not None:
        # Load data if path provided
        full_df = pd.read_csv(df_path)
        # Filter for calves and get 3x3 columns
        calves_df = full_df[full_df['category'] == 'Calf'].copy()
        calves_3x3 = calves_df[[col for col in calves_df.columns if col.endswith('_3x3')]].copy()
        calves_3x3 = calves_3x3.fillna(calves_3x3.mean())
    elif df is not None:
        calves_3x3 = df.copy()
    else:
        raise ValueError("Either df_path or df must be provided")
    
    # Set name for display
    calves_3x3.name = "Calves 3x3 Temperature Data"
    
    print(f"Analyzing correlations for {calves_3x3.shape[0]} calves across {calves_3x3.shape[1]} body parts")
    print(f"Body parts: {', '.join(calves_3x3.columns)}")
    
    # Calculate correlations
    correlation_matrix, correlation_pairs = calculate_pairwise_correlations(
        calves_3x3, 
        method='pearson',
        display_results=True,
        save_to_file='calves_3x3_correlations.csv'
    )
    
    # Display detailed correlation pairs if available
    if correlation_pairs is not None:
        print(f"\n{'='*80}")
        print("DETAILED PAIRWISE CORRELATIONS (sorted by strength)")
        print(f"{'='*80}")
        print("Significance levels: *** p<0.001, ** p<0.01, * p<0.05, ns = not significant")
        print(f"{'-'*80}")
        print(correlation_pairs.to_string(index=False))
    
    return correlation_matrix, correlation_pairs


# Example usage function
def example_usage():
    """
    Example of how to use the correlation analysis functions.
    """
    
    # Example 1: Using with file path
    print("Example 1: Loading data from file")
    try:
        corr_matrix, corr_pairs = analyze_calves_3x3_correlations(
            df_path="buffalo_1_3_5_max_values_ver-1.csv"
        )
    except FileNotFoundError:
        print("CSV file not found. Please provide the correct path.")
    
    # Example 2: Using with existing DataFrame (assuming calves_3x3 exists)
    print("\nExample 2: Using existing DataFrame")
    print("# Assuming you have calves_3x3 DataFrame already loaded:")
    print("# corr_matrix, corr_pairs = analyze_calves_3x3_correlations(df=calves_3x3)")


if __name__ == "__main__":
    example_usage()
