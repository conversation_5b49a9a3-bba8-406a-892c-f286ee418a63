import pandas as pd


def pairwise_correlation(df):
    """
    Calculate pairwise correlations for all numeric columns in a DataFrame.
    
    Parameters:
    -----------
    df : pandas.DataFrame
        Input DataFrame with numeric columns
    
    Returns:
    --------
    pandas.DataFrame
        DataFrame with columns: Variable1, Variable2, Correlation
        Sorted by correlation strength (descending)
    """
    
    columns = df.select_dtypes(include=['number']).columns
    results = []
    
    for i, col1 in enumerate(columns):
        for j, col2 in enumerate(columns):
            if i < j:  # Only upper triangle to avoid duplicates
                correlation = df[col1].corr(df[col2])
                results.append({
                    'Variable1': col1,
                    'Variable2': col2,
                    'Correlation': round(correlation, 4)
                })
    
    # Convert to DataFrame and sort by absolute correlation value (descending)
    result_df = pd.DataFrame(results)
    result_df = result_df.reindex(
        result_df['Correlation'].abs().sort_values(ascending=False).index
    ).reset_index(drop=True)
    
    return result_df


# Example usage:
# correlation_results = pairwise_correlation(calves_3x3)
