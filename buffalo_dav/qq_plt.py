import pandas as pd
import matplotlib.pyplot as plt
import scipy.stats as stats
import numpy as np

def qq_plot(df, dataset_name=None):
    """
    Create a single Q-Q plot for all numeric columns in a DataFrame, with each column plotted in a different color.

    Parameters:
    - df: pandas DataFrame with numeric columns to plot.
    - dataset_name: Optional string to include in the plot title.

    Returns:
    - None (displays a matplotlib plot).
    """
    # Select numeric columns
    columns = df.select_dtypes(include=["number"]).columns

    # Create a figure
    plt.figure(figsize=(8, 6))

    # Define a color cycle for different columns
    # colors = plt.cm.tab10(np.linspace(0, 1, len(columns)))

    for idx, col in enumerate(columns):
        # Drop NA values
        data = df[col].dropna()
        
        if len(data) < 3:
            # Skip columns with insufficient data
            print(f"Skipping {col}: fewer than 3 non-missing values.")
            continue
        
        # Generate Q-Q plot data
        (osm, osr), (slope, intercept, r) = stats.probplot(data, dist="norm")
        
        # Plot Q-Q points for this column
        plt.scatter(osm, osr, label=col, 
                    # color=colors[idx],
                    alpha=0.6, s=50)
        
        # Plot the reference line (y = x)
        plt.plot(osm, slope * osm + intercept, 
                #  color=colors[idx],
                 linestyle="--", alpha=0.5)

    # Add reference line (y = x) for clarity
    min_val = min(plt.xlim()[0], plt.ylim()[0])
    max_val = max(plt.xlim()[1], plt.ylim()[1])
    plt.plot([min_val, max_val], [min_val, max_val], color="black", linestyle="-", label="y=x")

    # Customize plot
    plt.xlabel("Theoretical Quantiles")
    plt.ylabel("Sample Quantiles")
    title = "Q-Q Plot for Numeric Columns"
    if dataset_name:
        title = f"Q-Q Plot for {dataset_name}"
    plt.title(title)
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Display plot
    plt.tight_layout()
    plt.show()