import pandas as pd
import matplotlib.pyplot as plt
import scipy.stats as stats
import numpy as np


def qq_plot(df, dataset_name=None):
    """
    Create a single Q-Q plot with all numeric columns overlaid.

    Parameters:
    -----------
    df : pandas.DataFrame
        Input DataFrame with numeric columns
    dataset_name : str, optional
        Name of the dataset to display in plot title

    Returns:
    --------
    None (displays matplotlib plot)
    """

    # Get numeric columns
    columns = df.select_dtypes(include=['number']).columns.tolist()

    if len(columns) == 0:
        print("No numeric columns found in DataFrame")
        return

    # Create single plot
    plt.figure(figsize=(8, 6))

    # Define colors for different columns
    colors = plt.cm.get_cmap('tab10')(np.linspace(0, 1, len(columns)))

    # Create Q-Q plot for each column
    for i, col in enumerate(columns):
        # Get clean data
        data = df[col].dropna()

        if len(data) < 3:
            print(f"Skipping {col}: insufficient data ({len(data)} points)")
            continue

        # Generate Q-Q plot data
        (theoretical_quantiles, sample_quantiles), (slope, intercept, r) = stats.probplot(data, dist="norm")

        # Shorten column name for legend
        short_name = col.replace('_max', '').replace('_3x3', '').replace('_5x5', '')

        # Plot Q-Q points
        plt.scatter(theoretical_quantiles, sample_quantiles,
                   color=colors[i], alpha=0.7, s=30, label=short_name)

        # Plot fitted line
        fitted_line = np.array(slope) * theoretical_quantiles + np.array(intercept)
        plt.plot(theoretical_quantiles, fitted_line,
                color=colors[i], linestyle='--', alpha=0.8)

    # Add perfect normal reference line (y = x)
    xlim = plt.xlim()
    ylim = plt.ylim()
    min_val = min(xlim[0], ylim[0])
    max_val = max(xlim[1], ylim[1])
    plt.plot([min_val, max_val], [min_val, max_val], 'k-', alpha=0.5, linewidth=1, label='Perfect Normal')

    # Customize plot
    plt.xlabel('Theoretical Quantiles')
    plt.ylabel('Sample Quantiles')

    if dataset_name:
        plt.title(f'Q-Q Plot - {dataset_name}')
    else:
        plt.title('Q-Q Plot - Normality Assessment')

    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.show()


# Example usage:
# qq_plot(calves_3x3, dataset_name="calves_3x3")