import pandas as pd


def mean_std_analysis(df, dataset_name=None):
    """
    Calculate mean ± std for all numeric columns in a DataFrame.

    Parameters:
    -----------
    df : pandas.DataFrame
        Input DataFrame with numeric columns
    dataset_name : str, optional
        Name of the dataset to display in results

    Returns:
    --------
    pandas.DataFrame
        DataFrame with columns: df_name, head, eye, thorax, abdomen, rectal, N_samples
        If dataset_name provided, shows dataset name in df_name column
    """

    # Get numeric columns
    columns = df.select_dtypes(include=['number']).columns.tolist()

    if len(columns) == 0:
        raise ValueError("No numeric columns found in DataFrame")

    # Initialize result dictionary with empty values
    result = {
        'df_name': dataset_name if dataset_name else 'Dataset',
        'head': '',
        'eye': '',
        'thorax': '',
        'abdomen': '',
        'rectal': '',
        'N_samples': 0
    }

    # Calculate mean ± std for each column and map to body parts
    total_samples = 0
    for col in columns:
        # Get clean data (remove NaN)
        data = df[col].dropna()

        if len(data) > 0:
            mean_val = data.mean()
            std_val = data.std()
            mean_std_str = f"{mean_val:.2f} ± {std_val:.2f}"
            total_samples = max(total_samples, len(data))

            # Map column to body part based on name
            col_lower = col.lower()
            if 'head' in col_lower:
                result['head'] = mean_std_str
            elif 'eye' in col_lower:
                result['eye'] = mean_std_str
            elif 'thorax' in col_lower:
                result['thorax'] = mean_std_str
            elif 'abdomen' in col_lower:
                result['abdomen'] = mean_std_str
            elif 'rectal' in col_lower:
                result['rectal'] = mean_std_str

    result['N_samples'] = total_samples

    # Convert to DataFrame
    result_df = pd.DataFrame([result])

    return result_df


# Example usage:
# mean_std_results = mean_std_analysis(calves_3x3, dataset_name="calves_3x3")
