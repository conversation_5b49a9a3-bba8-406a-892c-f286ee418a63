import pandas as pd


def mean_std_analysis(df, dataset_name=None):
    """
    Calculate mean ± std for all numeric columns in a DataFrame.
    
    Parameters:
    -----------
    df : pandas.DataFrame
        Input DataFrame with numeric columns
    dataset_name : str, optional
        Name of the dataset to display at the top of results
    
    Returns:
    --------
    pandas.DataFrame
        DataFrame with columns: Variable, Mean_Std
        If dataset_name provided, adds a row at the top with the dataset name
    """
    
    # Get numeric columns
    columns = df.select_dtypes(include=['number']).columns.tolist()
    
    if len(columns) == 0:
        raise ValueError("No numeric columns found in DataFrame")
    
    results = []
    
    # Calculate mean ± std for each column
    for col in columns:
        # Get clean data (remove NaN)
        data = df[col].dropna()
        
        if len(data) == 0:
            mean_std_str = "No data"
        else:
            mean_val = data.mean()
            std_val = data.std()
            mean_std_str = f"{mean_val:.2f} ± {std_val:.2f}"
        
        # Shorten column name for display
        short_name = col.replace('_max', '').replace('_3x3', '').replace('_5x5', '')
        
        results.append({
            'Variable': short_name,
            'Mean_Std': mean_std_str,
            'N_samples': len(data)
        })
    
    # Convert to DataFrame
    result_df = pd.DataFrame(results)
    
    # Add dataset name at the top if provided
    if dataset_name:
        header_row = pd.DataFrame({
            'Variable': [f'Dataset: {dataset_name}'],
            'Mean_Std': [''],
            'N_samples': ['']
        })
        result_df = pd.concat([header_row, result_df], ignore_index=True)
    
    return result_df


# Example usage:
# mean_std_results = mean_std_analysis(calves_3x3, dataset_name="calves_3x3")
