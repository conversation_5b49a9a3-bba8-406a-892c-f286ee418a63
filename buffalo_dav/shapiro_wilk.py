import pandas as pd
from scipy.stats import sha<PERSON><PERSON>


def shapiro_test(df, dataset_name=None,threshold=0.05):
    columns = df.select_dtypes(include=["number"]).columns
    results = []

    for col in columns:
        # Drop NA values for the test
        data = df[col].dropna()
        if len(data) < 3:
            # Shapiro requires at least 3 data points
            w_stat, p_value = None, None
        else:
            w_stat, p_value = shapiro(data)
        results.append(
            {
                "Variable": col,
                "W Statistic": round(w_stat, 4) if w_stat is not None else "",
                "p-value": round(p_value, 4) if p_value is not None else "",
                "significant": p_value > threshold
            }
        )

    result_df = pd.DataFrame(results)
    # Sort by p-value ascending (most non-normal first)
    result_df = result_df.sort_values("p-value", ascending=True).reset_index(drop=True)

    if dataset_name:
        header_row = pd.DataFrame(
            {
                "Variable": [f"Dataset: {dataset_name}"],
                "W Statistic": [""],
                "p-value": [""],
            }
        )
        result_df = pd.concat([header_row, result_df], ignore_index=True)

    return result_df

