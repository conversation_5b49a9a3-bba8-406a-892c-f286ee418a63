import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy import stats


def bland_altman_analysis(df, method1_col, method2_col, dataset_name=None):
    """
    Perform Bland-Altman analysis to assess agreement between two measurement methods.

    Parameters:
    -----------
    df : pandas.DataFrame
        Input DataFrame
    method1_col : str
        Name of the first method column (e.g., 'rectal_3x3')
    method2_col : str
        Name of the second method column (e.g., 'head_3x3')
    dataset_name : str, optional
        Name of the dataset to display in results

    Returns:
    --------
    pandas.DataFrame
        DataFrame with bias, limits of agreement, and statistics
        If dataset_name provided, adds a row at the top with the dataset name
    """

    # Clean data - remove rows with NaN in either column
    clean_data = df[[method1_col, method2_col]].dropna()

    if len(clean_data) < 3:
        raise ValueError(
            f"Insufficient data: only {len(clean_data)} complete pairs found"
        )

    method1 = clean_data[method1_col].values
    method2 = clean_data[method2_col].values

    # Calculate Bland-Altman statistics
    mean_values = (method1 + method2) / 2
    differences = method1 - method2

    # Calculate bias and limits of agreement
    bias = np.mean(differences)
    std_diff = np.std(differences, ddof=1)
    upper_loa = bias + 1.96 * std_diff
    lower_loa = bias - 1.96 * std_diff

    # Calculate 95% confidence intervals for bias and LOA
    n = len(differences)
    se_bias = std_diff / np.sqrt(n)
    se_loa = std_diff * np.sqrt(3 / n)

    t_critical = stats.t.ppf(0.975, n - 1)

    bias_ci_lower = bias - t_critical * se_bias
    bias_ci_upper = bias + t_critical * se_bias

    upper_loa_ci_lower = upper_loa - t_critical * se_loa
    upper_loa_ci_upper = upper_loa + t_critical * se_loa

    lower_loa_ci_lower = lower_loa - t_critical * se_loa
    lower_loa_ci_upper = lower_loa + t_critical * se_loa

    # Shorten column names for display
    short_method1 = (
        method1_col.replace("_max", "").replace("_3x3", "").replace("_5x5", "")
    )
    short_method2 = (
        method2_col.replace("_max", "").replace("_3x3", "").replace("_5x5", "")
    )

    # Create results
    results = [
        {
            "Comparison": f"{short_method1} vs {short_method2}",
            "N_samples": n,
            "Bias": round(bias, 4),
            "Bias_CI_Lower": round(bias_ci_lower, 4),
            "Bias_CI_Upper": round(bias_ci_upper, 4),
            "Upper_LOA": round(upper_loa, 4),
            "Lower_LOA": round(lower_loa, 4),
            "LOA_Range": round(upper_loa - lower_loa, 4),
        }
    ]

    # Convert to DataFrame
    results_df = pd.DataFrame(results)

    # Add dataset name at the top if provided
    if dataset_name:
        header_row = pd.DataFrame(
            {
                "Comparison": [f"Dataset: {dataset_name}"],
                "N_samples": [""],
                "Bias": [""],
                "Bias_CI_Lower": [""],
                "Bias_CI_Upper": [""],
                "Upper_LOA": [""],
                "Lower_LOA": [""],
                "LOA_Range": [""],
            }
        )
        results_df = pd.concat([header_row, results_df], ignore_index=True)

    # Create Bland-Altman plot
    plt.figure(figsize=(10, 6))

    # Scatter plot
    plt.scatter(mean_values, differences, alpha=0.6, s=50)

    # Bias line
    plt.axhline(
        bias, color="red", linestyle="-", linewidth=2, label=f"Bias: {bias:.3f}"
    )

    # Limits of agreement
    plt.axhline(
        upper_loa,
        color="red",
        linestyle="--",
        linewidth=1,
        label=f"Upper LOA: {upper_loa:.3f}",
    )
    plt.axhline(
        lower_loa,
        color="red",
        linestyle="--",
        linewidth=1,
        label=f"Lower LOA: {lower_loa:.3f}",
    )

    # Zero line
    plt.axhline(0, color="black", linestyle="-", alpha=0.3)

    # Labels and title
    plt.xlabel(f"Mean of {short_method1} and {short_method2}")
    plt.ylabel(f"Difference ({short_method1} - {short_method2})")

    if dataset_name:
        plt.title(
            f"Bland-Altman Plot - {dataset_name}\n{short_method1} vs {short_method2}"
        )
    else:
        plt.title(f"Bland-Altman Plot\n{short_method1} vs {short_method2}")

    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.show()

    return results_df


def bland_altman_multiple(df, reference_col, dataset_name=None):
    """
    Perform Bland-Altman analysis comparing multiple methods against a reference method.

    Parameters:
    -----------
    df : pandas.DataFrame
        Input DataFrame with numeric columns
    reference_col : str
        Name or partial name of the reference column (e.g., 'rectal')
    dataset_name : str, optional
        Name of the dataset to display in results

    Returns:
    --------
    pandas.DataFrame
        DataFrame with bias and LOA for each comparison
        Sorted by absolute bias (best agreement first)
    """

    # Get numeric columns
    all_columns = df.select_dtypes(include=["number"]).columns.tolist()

    # Find reference column by string matching
    ref_matches = [col for col in all_columns if reference_col.lower() in col.lower()]

    if len(ref_matches) == 0:
        raise ValueError(
            f"No column found containing '{reference_col}'. Available columns: {all_columns}"
        )
    elif len(ref_matches) > 1:
        print(f"Multiple columns found containing '{reference_col}': {ref_matches}")
        print(f"Using the first match: {ref_matches[0]}")

    actual_ref_col = ref_matches[0]

    # Get comparison columns (excluding the reference)
    comparison_columns = [col for col in all_columns if col != actual_ref_col]

    if len(comparison_columns) < 1:
        raise ValueError("Need at least 1 comparison column for Bland-Altman analysis")

    all_results = []

    # Compare each method against the reference
    for comp_col in comparison_columns:
        try:
            result = bland_altman_analysis(df, actual_ref_col, comp_col)
            # Extract the data row (skip header if present)
            data_row = result.iloc[-1] if len(result) > 1 else result.iloc[0]
            all_results.append(data_row.to_dict())
        except ValueError as e:
            print(f"Skipping {comp_col}: {e}")
            continue

    if not all_results:
        raise ValueError("No valid comparisons could be performed")

    # Convert to DataFrame and sort by absolute bias
    results_df = pd.DataFrame(all_results)
    results_df["Abs_Bias"] = abs(results_df["Bias"])
    results_df = (
        results_df.sort_values("Abs_Bias")
        .drop("Abs_Bias", axis=1)
        .reset_index(drop=True)
    )

    # Add dataset name at the top if provided
    if dataset_name:
        header_row = pd.DataFrame(
            {
                "Comparison": [f"Dataset: {dataset_name}"],
                "N_samples": [""],
                "Bias": [""],
                "Bias_CI_Lower": [""],
                "Bias_CI_Upper": [""],
                "Upper_LOA": [""],
                "Lower_LOA": [""],
                "LOA_Range": [""],
            }
        )
        results_df = pd.concat([header_row, results_df], ignore_index=True)

    return results_df


# Example usage:
# Single comparison
# ba_result = bland_altman_analysis(calves_3x3, 'rectal_3x3', 'head_3x3', dataset_name="calves_3x3")

# Multiple comparisons against reference
# ba_results = bland_altman_multiple(calves_3x3, 'rectal', dataset_name="calves_3x3")
