{"cells": [{"cell_type": "code", "execution_count": 1, "id": "beaa52cd", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from scipy.stats import ttest_rel\n", "from paired_t_test import paired_t_test\n", "from pairwise_correlation import *\n", "from anova_analysis import *\n", "from oneway_anova import *\n", "from linear_regression_analysis import *\n"]}, {"cell_type": "code", "execution_count": 2, "id": "38792929", "metadata": {}, "outputs": [], "source": ["df = pd.read_csv(\"buffalo_1_3_5_max_values_ver-1.csv\")"]}, {"cell_type": "markdown", "id": "49c00a6a", "metadata": {}, "source": ["# Data Preprocessing"]}, {"cell_type": "code", "execution_count": 3, "id": "08efd527", "metadata": {}, "outputs": [], "source": ["try:\n", "    df = df.drop(columns=[\"processed_datetime\", \"animal_id\"])\n", "except:\n", "    pass\n", "df_cols = df.columns\n", "# df\n", "calves_df = df[df[\"category\"] == \"Calf\"].copy()\n", "lactating_df = df[df[\"category\"] == \"Lactating\"].copy()\n", "pregnant_df = df[df[\"category\"] == \"Pregnant\"].copy()\n", "if \"category\" in calves_df.columns:\n", "    calves_df = calves_df.drop(columns=[\"category\"])\n", "if \"category\" in pregnant_df.columns:\n", "    pregnant_df = pregnant_df.drop(columns=[\"category\"])\n", "if \"category\" in lactating_df.columns:\n", "    lactating_df = lactating_df.drop(columns=[\"category\"])\n", "\n", "if \"category\" in df.columns:\n", "    buffalo_df = df.drop(columns=[\"category\"])\n", "buffalo_df = buffalo_df.fillna(buffalo_df.mean()).round(3)\n", "buffalo_df_max = buffalo_df[\n", "    [col for col in buffalo_df.columns if col.endswith(\"_max\")]\n", "].copy()\n", "buffalo_df_3x3 = buffalo_df[\n", "    [col for col in buffalo_df.columns if col.endswith(\"_3x3\")]\n", "].copy()\n", "buffalo_df_5x5 = buffalo_df[\n", "    [col for col in buffalo_df.columns if col.endswith(\"_5x5\")]\n", "].copy()\n", "# buffalo_df_5x5\n", "# print(calves_df.mean())\n", "# print(calves_df.std())\n", "calves_df = calves_df.fillna(calves_df.mean()).round(decimals=2)\n", "pregnant_df = pregnant_df.fillna(pregnant_df.mean()).round(decimals=2)\n", "lactating_df = lactating_df.fillna(lactating_df.mean()).round(decimals=2)\n", "# print(calves_df.mean())\n", "# # print(calves_df.std())\n", "# calves_df.describe()\n", "# pregnant_df.describe()\n", "# lactating_df.describe()\n", "\n", "# calves_max = calves_df[\n", "#     [col for col in calves_df.columns if col.endswith(\"_max\")]\n", "# ].copy()\n", "# calves_max"]}, {"cell_type": "code", "execution_count": 4, "id": "4557518f", "metadata": {}, "outputs": [], "source": ["category = [\"calves\", \"pregnant\", \"lactating\"]\n", "suffixes = [\"max\", \"3x3\", \"5x5\"]\n", "\n", "dataset_names = []\n", "for part in category:\n", "    for suffix in suffixes:\n", "        dataset_name = str(part) + \"_\" + str(suffix)\n", "        dataset_names.append(dataset_name)\n", "\n", "dataset = []\n", "dataset.clear()\n", "import pandas as pd\n", "\n", "for i in range(len(dataset_names)):\n", "    df_pair = {\"name\": None, \"ds\": None}\n", "    df_pair[\"name\"] = dataset_names[i]\n", "    dataset.append(df_pair)\n", "\n", "dataset.append({\"ds\": buffalo_df_max, \"name\": \"buffalo_df_max\"})\n", "dataset.append({\"ds\": buffalo_df_3x3, \"name\": \"buffalo_df_3x3\"})\n", "dataset.append({\"ds\": buffalo_df_5x5, \"name\": \"buffalo_df_5x5\"})\n", "\n", "# -------------------------------------------BRUTE FORCE APPROACH-------------------------------------------\n", "for ds in dataset:\n", "    if ds[\"name\"] == \"calves_max\":\n", "        ds[\"ds\"] = calves_df[\n", "            [col for col in calves_df.columns if col.endswith(\"_max\")]\n", "        ].copy()\n", "\n", "for ds in dataset:\n", "    if ds[\"name\"] == \"calves_3x3\":\n", "        ds[\"ds\"] = calves_df[\n", "            [col for col in calves_df.columns if col.endswith(\"_3x3\")]\n", "        ].copy()\n", "# calves_5x5 = calves_df[[col for col in calves_df.columns if col.endswith(\"_5x5\")]].copy()\n", "for ds in dataset:\n", "    if ds[\"name\"] == \"calves_5x5\":\n", "        ds[\"ds\"] = calves_df[\n", "            [col for col in calves_df.columns if col.endswith(\"_5x5\")]\n", "        ].copy()\n", "\n", "for ds in dataset:\n", "    if ds[\"name\"] == \"lactating_5x5\":\n", "        ds[\"ds\"] = lactating_df[\n", "            [col for col in lactating_df.columns if col.endswith(\"_5x5\")]\n", "        ].copy()\n", "\n", "for ds in dataset:\n", "    if ds[\"name\"] == \"lactating_3x3\":\n", "        ds[\"ds\"] = lactating_df[\n", "            [col for col in lactating_df.columns if col.endswith(\"_3x3\")]\n", "        ].copy()\n", "\n", "for ds in dataset:\n", "    if ds[\"name\"] == \"lactating_max\":\n", "        ds[\"ds\"] = lactating_df[\n", "            [col for col in lactating_df.columns if col.endswith(\"_max\")]\n", "        ].copy()\n", "\n", "for ds in dataset:\n", "    if ds[\"name\"] == \"pregnant_5x5\":\n", "        ds[\"ds\"] = pregnant_df[\n", "            [col for col in pregnant_df.columns if col.endswith(\"_5x5\")]\n", "        ].copy()\n", "\n", "for ds in dataset:\n", "    if ds[\"name\"] == \"pregnant_max\":\n", "        ds[\"ds\"] = pregnant_df[\n", "            [col for col in pregnant_df.columns if col.endswith(\"_max\")]\n", "        ].copy()\n", "\n", "for ds in dataset:\n", "    if ds[\"name\"] == \"pregnant_3x3\":\n", "        ds[\"ds\"] = pregnant_df[\n", "            [col for col in pregnant_df.columns if col.endswith(\"_3x3\")]\n", "        ].copy()\n", "\n", "for i in dataset:\n", "    # print(i['name'])\n", "    pass"]}, {"cell_type": "markdown", "id": "bc9bc4f7", "metadata": {}, "source": ["# Paired t-tests\n"]}, {"cell_type": "code", "execution_count": 5, "id": "253c5916", "metadata": {}, "outputs": [], "source": ["ptt = paired_t_test(df=dataset[-1]['ds'], threshold=0.05, dataset_name=dataset[-1]['name'])\n", "# ptt"]}, {"cell_type": "markdown", "id": "9f36eeae", "metadata": {}, "source": ["# Pairwise Correlation"]}, {"cell_type": "code", "execution_count": 6, "id": "fa3d6c62", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Variable1</th>\n", "      <th>Variable2</th>\n", "      <th>Correlation</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Dataset: buffalo_df_5x5</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>thorax_5x5</td>\n", "      <td>abdomen_5x5</td>\n", "      <td>0.9049</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>head_5x5</td>\n", "      <td>abdomen_5x5</td>\n", "      <td>0.8584</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>head_5x5</td>\n", "      <td>thorax_5x5</td>\n", "      <td>0.8374</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>abdomen_5x5</td>\n", "      <td>rectal_5x5</td>\n", "      <td>0.8351</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>head_5x5</td>\n", "      <td>eye_5x5</td>\n", "      <td>0.8115</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>thorax_5x5</td>\n", "      <td>rectal_5x5</td>\n", "      <td>0.7672</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>eye_5x5</td>\n", "      <td>abdomen_5x5</td>\n", "      <td>0.723</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>head_5x5</td>\n", "      <td>rectal_5x5</td>\n", "      <td>0.7096</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>eye_5x5</td>\n", "      <td>thorax_5x5</td>\n", "      <td>0.7009</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>eye_5x5</td>\n", "      <td>rectal_5x5</td>\n", "      <td>0.5898</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                  Variable1    Variable2 Correlation\n", "0   Dataset: buffalo_df_5x5                         \n", "1                thorax_5x5  abdomen_5x5      0.9049\n", "2                  head_5x5  abdomen_5x5      0.8584\n", "3                  head_5x5   thorax_5x5      0.8374\n", "4               abdomen_5x5   rectal_5x5      0.8351\n", "5                  head_5x5      eye_5x5      0.8115\n", "6                thorax_5x5   rectal_5x5      0.7672\n", "7                   eye_5x5  abdomen_5x5       0.723\n", "8                  head_5x5   rectal_5x5      0.7096\n", "9                   eye_5x5   thorax_5x5      0.7009\n", "10                  eye_5x5   rectal_5x5      0.5898"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["pcc =pairwise_correlation(df=dataset[-1]['ds'], dataset_name=dataset[-1]['name'], meth='spearman')\n", "pcc"]}, {"cell_type": "code", "execution_count": 7, "id": "f67f1fcb", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Variable1</th>\n", "      <th>Variable2</th>\n", "      <th>Correlation</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Dataset: buffalo_df_5x5</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>thorax_5x5</td>\n", "      <td>abdomen_5x5</td>\n", "      <td>0.8992</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>head_5x5</td>\n", "      <td>eye_5x5</td>\n", "      <td>0.8376</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>abdomen_5x5</td>\n", "      <td>rectal_5x5</td>\n", "      <td>0.8368</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>head_5x5</td>\n", "      <td>abdomen_5x5</td>\n", "      <td>0.818</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>head_5x5</td>\n", "      <td>thorax_5x5</td>\n", "      <td>0.7952</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>thorax_5x5</td>\n", "      <td>rectal_5x5</td>\n", "      <td>0.7626</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>eye_5x5</td>\n", "      <td>abdomen_5x5</td>\n", "      <td>0.6972</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>head_5x5</td>\n", "      <td>rectal_5x5</td>\n", "      <td>0.6759</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>eye_5x5</td>\n", "      <td>thorax_5x5</td>\n", "      <td>0.598</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>eye_5x5</td>\n", "      <td>rectal_5x5</td>\n", "      <td>0.5572</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                  Variable1    Variable2 Correlation\n", "0   Dataset: buffalo_df_5x5                         \n", "1                thorax_5x5  abdomen_5x5      0.8992\n", "2                  head_5x5      eye_5x5      0.8376\n", "3               abdomen_5x5   rectal_5x5      0.8368\n", "4                  head_5x5  abdomen_5x5       0.818\n", "5                  head_5x5   thorax_5x5      0.7952\n", "6                thorax_5x5   rectal_5x5      0.7626\n", "7                   eye_5x5  abdomen_5x5      0.6972\n", "8                  head_5x5   rectal_5x5      0.6759\n", "9                   eye_5x5   thorax_5x5       0.598\n", "10                  eye_5x5   rectal_5x5      0.5572"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["pcc =pairwise_correlation(df=dataset[-1]['ds'], dataset_name=dataset[-1]['name'], meth='pearson')\n", "pcc"]}, {"cell_type": "markdown", "id": "2842f41d", "metadata": {}, "source": ["# anova analysis\n", "\n", "- ❌ Columns are not independent\n", "\n", "- ✅ We need RM-ANOVA (not standard ANOVA)"]}, {"cell_type": "code", "execution_count": 8, "id": "8ff4804c", "metadata": {}, "outputs": [], "source": ["anova = rm_anova_analysis(dataset[-1]['ds'], dataset_name=dataset[-1]['name'])\n", "# anova"]}, {"cell_type": "code", "execution_count": 9, "id": "ed3af983", "metadata": {}, "outputs": [], "source": ["one_way_anova = oneway_anova_analysis(dataset[-1]['ds'], dataset_name=dataset[-1]['name'])\n", "# one_way_anova"]}, {"cell_type": "markdown", "id": "ec7027ed", "metadata": {}, "source": ["# Linear Regression"]}, {"cell_type": "code", "execution_count": 10, "id": "ee358506", "metadata": {}, "outputs": [], "source": ["target = \"rectal_\" + dataset[0]['name'].split('_')[-1]\n", "lra = linear_regression_analysis(dataset[0]['ds'],target_column=target, dataset_name=dataset[0]['name'])\n", "# lra"]}, {"cell_type": "markdown", "id": "61eacd89", "metadata": {}, "source": ["R² (coefficient of determination): % of rectal temperature variation explained by surface temp\n", "\n", "0.77 ⇒ thorax explains 77.6% of rectal temp variance ⇒ strong\n", "\n", "0.38 ⇒ head explains only 38.5% ⇒ weak\n", "\n", "RMSE / MAE: Error between predicted and actual rectal values\n", "\n", "Lower = better = closer predictions\n"]}, {"cell_type": "markdown", "id": "1c70104f", "metadata": {}, "source": ["# Shapiro-Wilk Test (test for Normality)"]}, {"cell_type": "code", "execution_count": 11, "id": "e5da3ce2", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Variable</th>\n", "      <th>W Statistic</th>\n", "      <th>p-value</th>\n", "      <th>significant</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Dataset: buffalo_df_5x5</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>thorax_5x5</td>\n", "      <td>0.9214</td>\n", "      <td>0.0053</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>eye_5x5</td>\n", "      <td>0.9405</td>\n", "      <td>0.0247</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>rectal_5x5</td>\n", "      <td>0.9499</td>\n", "      <td>0.0545</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>abdomen_5x5</td>\n", "      <td>0.9601</td>\n", "      <td>0.1316</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>head_5x5</td>\n", "      <td>0.9633</td>\n", "      <td>0.1731</td>\n", "      <td>True</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                  Variable W Statistic p-value significant\n", "0  Dataset: buffalo_df_5x5                             NaN\n", "1               thorax_5x5      0.9214  0.0053       False\n", "2                  eye_5x5      0.9405  0.0247       False\n", "3               rectal_5x5      0.9499  0.0545        True\n", "4              abdomen_5x5      0.9601  0.1316        True\n", "5                 head_5x5      0.9633  0.1731        True"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["from shapiro_wilk import *\n", "sw = shapiro_test(dataset[-1][\"ds\"], dataset_name=dataset[-1]['name'])\n", "sw"]}, {"cell_type": "markdown", "id": "3bf1f909", "metadata": {}, "source": ["# Q-Q Plot"]}, {"cell_type": "code", "execution_count": 12, "id": "24694fc4", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from qq_plt import qq_plot\n", "qq_plot(dataset[-1][\"ds\"], dataset_name=dataset[-1]['name'])"]}, {"cell_type": "markdown", "id": "5d1ba43e", "metadata": {}, "source": ["# <PERSON><PERSON> Test"]}, {"cell_type": "code", "execution_count": null, "id": "ef5592fa", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "38b8c94f", "metadata": {}, "source": ["# Final pipleline loop"]}, {"cell_type": "code", "execution_count": 13, "id": "a860cc9d", "metadata": {}, "outputs": [], "source": ["df_name = None\n", "df = pd.DataFrame()\n", "final_df = pd.DataFrame()\n", "separater_df = pd.DataFrame(columns=[\"blank\"])\n", "for i in range(len(dataset)):\n", "    df = dataset[i][\"ds\"]\n", "    df_name = dataset[i][\"name\"]\n", "    # correlation\n", "    correlation_df = pairwise_correlation(df=df, dataset_name=df_name)\n", "    # paired_t_test\n", "    ptt = paired_t_test(df=df, dataset_name=df_name)\n", "    # rm_anova\n", "    rm_anova = rm_anova_analysis(df=df, dataset_name=df_name)\n", "    # lra\n", "    target = \"rectal_\" + dataset[i][\"name\"].split(\"_\")[-1]\n", "    lra = linear_regression_analysis(df=df, target_column=target, dataset_name=df_name)\n", "    shapiro_wilki = shapiro_test(df=df, dataset_name=df_name)\n", "    # if i == 2:\n", "    df3 = pd.concat(\n", "        [\n", "            \n", "            correlation_df,\n", "            separater_df,\n", "            ptt,\n", "            separater_df,\n", "            rm_anova,\n", "            separater_df,\n", "            lra,\n", "            separater_df,\n", "            sha<PERSON><PERSON>_wilki,\n", "            separater_df,\n", "        ],\n", "        axis=1,\n", "    )\n", "    # print(df3)\n", "    df3.columns = [\n", "        f\"{col}_{i}\" if df3.columns.duplicated()[i] else col\n", "        for i, col in enumerate(df3.columns)\n", "    ]\n", "\n", "    # Create an empty row DataFrame with the same columns\n", "    empty_row = pd.DataFrame({col: [None] for col in df3.columns})\n", "    # empty_row = pd.concat([empty_row, empt])\n", "    # Concatenate DataFrames with the empty row in between\n", "    # result = pd.concat([df1, empty_row, df2], ignore_index=True)\n", "\n", "    final_df = pd.concat(\n", "        [final_df, empty_row, df3],\n", "        axis=0,\n", "    )\n", "\n", "# final_df[final_df.select_dtypes(include=\"number\").columns] = \\\n", "    # final_df.select_dtypes(include=\"number\").round(2)\n", "\n", "\n", "# final_df.dtypes\n", "\n", "final_df.to_csv(\"/tmp/temperature_analysis_summary_all_groups.csv\")\n", "dff = pd.read_csv(\"/tmp/temperature_analysis_summary_all_groups.csv\")\n", "dff = dff.round(2)\n", "dff.to_csv(\"temperature_analysis_summary_all_groups.csv\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}