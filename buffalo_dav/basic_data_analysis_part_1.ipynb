import pandas as pd
# from scipy.stats import ttest_rel
# from paired_t_test import paired_t_test
from pairwise_correlation import *
from anova_analysis import *
from oneway_anova import *
from linear_regression_analysis import *
from bland_altman import *


df = pd.read_csv("buffalo_1_3_5_max_values_ver-1.csv")

try:
    df = df.drop(columns=["processed_datetime", "animal_id"])
except:
    pass
df_cols = df.columns
# df
calves_df = df[df["category"] == "Calf"].copy()
lactating_df = df[df["category"] == "Lactating"].copy()
pregnant_df = df[df["category"] == "Pregnant"].copy()
if "category" in calves_df.columns:
    calves_df = calves_df.drop(columns=["category"])
if "category" in pregnant_df.columns:
    pregnant_df = pregnant_df.drop(columns=["category"])
if "category" in lactating_df.columns:
    lactating_df = lactating_df.drop(columns=["category"])

if "category" in df.columns:
    buffalo_df = df.drop(columns=["category"])
buffalo_df = buffalo_df.fillna(buffalo_df.mean()).round(3)
buffalo_df_max = buffalo_df[
    [col for col in buffalo_df.columns if col.endswith("_max")]
].copy()
buffalo_df_3x3 = buffalo_df[
    [col for col in buffalo_df.columns if col.endswith("_3x3")]
].copy()
buffalo_df_5x5 = buffalo_df[
    [col for col in buffalo_df.columns if col.endswith("_5x5")]
].copy()
# buffalo_df_5x5
# print(calves_df.mean())
# print(calves_df.std())
calves_df = calves_df.fillna(calves_df.mean()).round(decimals=2)
pregnant_df = pregnant_df.fillna(pregnant_df.mean()).round(decimals=2)
lactating_df = lactating_df.fillna(lactating_df.mean()).round(decimals=2)
# print(calves_df.mean())
# # print(calves_df.std())
# calves_df.describe()
# pregnant_df.describe()
# lactating_df.describe()

# calves_max = calves_df[
#     [col for col in calves_df.columns if col.endswith("_max")]
# ].copy()
# calves_max

category = ["calves", "pregnant", "lactating"]
suffixes = ["max", "3x3", "5x5"]

dataset_names = []
for part in category:
    for suffix in suffixes:
        dataset_name = str(part) + "_" + str(suffix)
        dataset_names.append(dataset_name)

dataset = []
dataset.clear()
import pandas as pd

for i in range(len(dataset_names)):
    df_pair = {"name": None, "ds": None}
    df_pair["name"] = dataset_names[i]
    dataset.append(df_pair)

dataset.append({"ds": buffalo_df_max, "name": "buffalo_df_max"})
dataset.append({"ds": buffalo_df_3x3, "name": "buffalo_df_3x3"})
dataset.append({"ds": buffalo_df_5x5, "name": "buffalo_df_5x5"})

# -------------------------------------------BRUTE FORCE APPROACH-------------------------------------------
for ds in dataset:
    if ds["name"] == "calves_max":
        ds["ds"] = calves_df[
            [col for col in calves_df.columns if col.endswith("_max")]
        ].copy()

for ds in dataset:
    if ds["name"] == "calves_3x3":
        ds["ds"] = calves_df[
            [col for col in calves_df.columns if col.endswith("_3x3")]
        ].copy()
# calves_5x5 = calves_df[[col for col in calves_df.columns if col.endswith("_5x5")]].copy()
for ds in dataset:
    if ds["name"] == "calves_5x5":
        ds["ds"] = calves_df[
            [col for col in calves_df.columns if col.endswith("_5x5")]
        ].copy()

for ds in dataset:
    if ds["name"] == "lactating_5x5":
        ds["ds"] = lactating_df[
            [col for col in lactating_df.columns if col.endswith("_5x5")]
        ].copy()

for ds in dataset:
    if ds["name"] == "lactating_3x3":
        ds["ds"] = lactating_df[
            [col for col in lactating_df.columns if col.endswith("_3x3")]
        ].copy()

for ds in dataset:
    if ds["name"] == "lactating_max":
        ds["ds"] = lactating_df[
            [col for col in lactating_df.columns if col.endswith("_max")]
        ].copy()

for ds in dataset:
    if ds["name"] == "pregnant_5x5":
        ds["ds"] = pregnant_df[
            [col for col in pregnant_df.columns if col.endswith("_5x5")]
        ].copy()

for ds in dataset:
    if ds["name"] == "pregnant_max":
        ds["ds"] = pregnant_df[
            [col for col in pregnant_df.columns if col.endswith("_max")]
        ].copy()

for ds in dataset:
    if ds["name"] == "pregnant_3x3":
        ds["ds"] = pregnant_df[
            [col for col in pregnant_df.columns if col.endswith("_3x3")]
        ].copy()

for i in dataset:
    # print(i['name'])
    pass

# ptt = paired_t_test(df=dataset[-1]['ds'], threshold=0.05, dataset_name=dataset[-1]['name'])
# ptt

pcc =pairwise_correlation_spearman(df=dataset[-1]['ds'], dataset_name=dataset[-1]['name'], meth='spearman')
# pcc

pcc =pairwise_correlation_pearson(df=dataset[-1]['ds'], dataset_name=dataset[-1]['name'], meth='pearson')
# pcc

anova = rm_anova_analysis(dataset[-1]['ds'], dataset_name=dataset[-1]['name'])
# anova

one_way_anova = oneway_anova_analysis(dataset[-1]['ds'], dataset_name=dataset[-1]['name'])
# one_way_anova

target = "rectal_" + dataset[0]['name'].split('_')[-1]
lra = linear_regression_analysis(dataset[0]['ds'],target_column=target, dataset_name=dataset[0]['name'])
lra

from shapiro_wilk import *
sw = shapiro_test(dataset[-1]["ds"], dataset_name=dataset[-1]['name'])
sw

import pandas as pd
import matplotlib.pyplot as plt
import scipy.stats as stats
import numpy as np
# Create subplots — auto-layout
# num_cols = numeric_df.shape[1]
num_cols = 5
fig, axes = plt.subplots(1, num_cols, figsize=(5 * num_cols, 5))

# If only one column, axes isn't a list
if num_cols == 1:
    axes = [axes]

# Draw QQ plots
for i, col in enumerate(dataset[-1]['ds'].columns):
    stats.probplot(dataset[-1]['ds'][col], dist="norm", plot=axes[i])
    axes[i].set_title(f"QQ Plot - {col}")

plt.tight_layout()
plt.show()

# %load_ext autoreload
# %autoreload 2

# from qq_plt import qq_plot
# # if qq_plot in sys.modules:
# #     del sys.modules[qq_plot]
#     # import sys
# import scipy
# # scipy.stat.pr
# # qq_plot(dataset[-1]["ds"], dataset_name=dataset[-1]['name'])

from nonparametric_tests import *
mw = mann_whitney_test(dataset[-1]['ds'], dataset_name=dataset[-1]['name'])
mw

kw = kruskal_wallis_test(dataset[-1]['ds'], dataset_name=dataset[-1]['name'])
kw

from bland_altman import *
# ba = bland_altman_analysis()

df_name = None
df = pd.DataFrame()
final_df = pd.DataFrame()
separater_df = pd.DataFrame(columns=["blank"])
for i in range(len(dataset)):
    df = dataset[i]["ds"]
    df_name = dataset[i]["name"]
    # correlation
    correlation_df_spearman = pairwise_correlation_spearman(
        df=df, dataset_name=df_name, meth="spearman"
    )
    correlation_df_pearson = pairwise_correlation_pearson(
        df=df, dataset_name=df_name, meth="pearson"
    )
    # paired_t_test
    # ptt = paired_t_test(df=df, dataset_name=df_name)
    # rm_anova
    rm_anova = rm_anova_analysis(df=df, dataset_name=df_name)
    # lra
    target = "rectal_" + dataset[i]["name"].split("_")[-1]
    lra = linear_regression_analysis(df=df, target_column=target, dataset_name=df_name)
    # Shapiro-wilk Test
    shapiro_wilki = shapiro_test(df=df, dataset_name=df_name)
    # Mann Whitney Test
    mann_whitney = mann_whitney_test(df=df, dataset_name=df_name)
    # Kruskal Wali Test
    kruskal_wali = kruskal_wallis_test(df=df, dataset_name=df_name)
    # Bland Altman
    bland_alt = bland_altman_pairwise(df=df, dataset_name=df_name)
    # if i == 2:
    df3 = pd.concat(
        [
            correlation_df_pearson,
            separater_df,
            correlation_df_spearman,
            # separater_df,
            # ptt,
            separater_df,
            mann_whitney,
            separater_df,
            rm_anova,
            separater_df,
            kruskal_wali,
            lra,
            separater_df,
            shapiro_wilki,
            separater_df,
            bland_alt
        ],
        axis=1,
    )
    # print(df3)
    df3.columns = [
        f"{col}_{i}" if df3.columns.duplicated()[i] else col
        for i, col in enumerate(df3.columns)
    ]

    # Create an empty row DataFrame with the same columns
    empty_row = pd.DataFrame({col: [None] for col in df3.columns})
    # empty_row = pd.concat([empty_row, empt])
    # Concatenate DataFrames with the empty row in between
    # result = pd.concat([df1, empty_row, df2], ignore_index=True)

    final_df = pd.concat(
        [final_df, empty_row, df3],
        axis=0,
    )

# final_df[final_df.select_dtypes(include="number").columns] = \
# final_df.select_dtypes(include="number").round(2)


# final_df.dtypes

final_df.to_csv("/tmp/temperature_analysis_summary_all_groups.csv")
dff = pd.read_csv("/tmp/temperature_analysis_summary_all_groups.csv")
dff = dff.round(2)
dff.to_csv("temperature_analysis_summary_all_groups.csv")

from mean_std_analysis import mean_std_analysis

# Get mean ± std for all columns
mean_std_results = mean_std_analysis(buffalo_df_3x3, dataset_name="calves_3x3")
mean_std_results

df_name = None
df = pd.DataFrame()
final_df = pd.DataFrame()
separater_df = pd.DataFrame(columns=["blank"])
for i in range(len(dataset)):
    df = dataset[i]["ds"]
    df_name = dataset[i]["name"]
    # correlation
    mean_std_res = mean_std_analysis(
        df=df, dataset_name=df_name
    )
  
    df3 = pd.concat(
        [
            mean_std_res,
            separater_df,
        ],
        axis=1,
    )
    # print(df3)
    df3.columns = [
        f"{col}_{i}" if df3.columns.duplicated()[i] else col
        for i, col in enumerate(df3.columns)
    ]

    # Create an empty row DataFrame with the same columns
    empty_row = pd.DataFrame({col: [None] for col in df3.columns})
    # empty_row = pd.concat([empty_row, empt])
    # Concatenate DataFrames with the empty row in between
    # result = pd.concat([df1, empty_row, df2], ignore_index=True)

    final_df = pd.concat(
        [final_df, empty_row, df3],
        axis=0,
    )

# final_df[final_df.select_dtypes(include="number").columns] = \
# final_df.select_dtypes(include="number").round(2)


# final_df.dtypes

final_df.to_csv("/tmp/temperature_analysis_summary_all_groups.csv")
dff = pd.read_csv("/tmp/temperature_analysis_summary_all_groups.csv")
dff = dff.round(2)
dff.to_csv("temperature_analysis_summary_all_groups.csv")