import pandas as pd
from scipy.stats import ttest_rel
from paired_t_test import paired_t_test
from pairwise_correlation import *
from anova_analysis import *
from oneway_anova import *
from linear_regression_analysis import *


df = pd.read_csv("buffalo_1_3_5_max_values_ver-1.csv")

try:
    df = df.drop(columns=["processed_datetime", "animal_id"])
except:
    pass
df_cols = df.columns
# df
calves_df = df[df["category"] == "Calf"].copy()
lactating_df = df[df["category"] == "Lactating"].copy()
pregnant_df = df[df["category"] == "Pregnant"].copy()
if "category" in calves_df.columns:
    calves_df = calves_df.drop(columns=["category"])
if "category" in pregnant_df.columns:
    pregnant_df = pregnant_df.drop(columns=["category"])
if "category" in lactating_df.columns:
    lactating_df = lactating_df.drop(columns=["category"])

if "category" in df.columns:
    buffalo_df = df.drop(columns=["category"])
buffalo_df = buffalo_df.fillna(buffalo_df.mean()).round(3)
buffalo_df_max = buffalo_df[
    [col for col in buffalo_df.columns if col.endswith("_max")]
].copy()
buffalo_df_3x3 = buffalo_df[
    [col for col in buffalo_df.columns if col.endswith("_3x3")]
].copy()
buffalo_df_5x5 = buffalo_df[
    [col for col in buffalo_df.columns if col.endswith("_5x5")]
].copy()
# buffalo_df_5x5
# print(calves_df.mean())
# print(calves_df.std())
calves_df = calves_df.fillna(calves_df.mean()).round(decimals=2)
pregnant_df = pregnant_df.fillna(pregnant_df.mean()).round(decimals=2)
lactating_df = lactating_df.fillna(lactating_df.mean()).round(decimals=2)
# print(calves_df.mean())
# # print(calves_df.std())
# calves_df.describe()
# pregnant_df.describe()
# lactating_df.describe()

# calves_max = calves_df[
#     [col for col in calves_df.columns if col.endswith("_max")]
# ].copy()
# calves_max

category = ["calves", "pregnant", "lactating"]
suffixes = ["max", "3x3", "5x5"]

dataset_names = []
for part in category:
    for suffix in suffixes:
        dataset_name = str(part) + "_" + str(suffix)
        dataset_names.append(dataset_name)

dataset = []
dataset.clear()
import pandas as pd

for i in range(len(dataset_names)):
    df_pair = {"name": None, "ds": None}
    df_pair["name"] = dataset_names[i]
    dataset.append(df_pair)

dataset.append({"ds": buffalo_df_max, "name": "buffalo_df_max"})
dataset.append({"ds": buffalo_df_3x3, "name": "buffalo_df_3x3"})
dataset.append({"ds": buffalo_df_5x5, "name": "buffalo_df_5x5"})

# -------------------------------------------BRUTE FORCE APPROACH-------------------------------------------
for ds in dataset:
    if ds["name"] == "calves_max":
        ds["ds"] = calves_df[
            [col for col in calves_df.columns if col.endswith("_max")]
        ].copy()

for ds in dataset:
    if ds["name"] == "calves_3x3":
        ds["ds"] = calves_df[
            [col for col in calves_df.columns if col.endswith("_3x3")]
        ].copy()
# calves_5x5 = calves_df[[col for col in calves_df.columns if col.endswith("_5x5")]].copy()
for ds in dataset:
    if ds["name"] == "calves_5x5":
        ds["ds"] = calves_df[
            [col for col in calves_df.columns if col.endswith("_5x5")]
        ].copy()

for ds in dataset:
    if ds["name"] == "lactating_5x5":
        ds["ds"] = lactating_df[
            [col for col in lactating_df.columns if col.endswith("_5x5")]
        ].copy()

for ds in dataset:
    if ds["name"] == "lactating_3x3":
        ds["ds"] = lactating_df[
            [col for col in lactating_df.columns if col.endswith("_3x3")]
        ].copy()

for ds in dataset:
    if ds["name"] == "lactating_max":
        ds["ds"] = lactating_df[
            [col for col in lactating_df.columns if col.endswith("_max")]
        ].copy()

for ds in dataset:
    if ds["name"] == "pregnant_5x5":
        ds["ds"] = pregnant_df[
            [col for col in pregnant_df.columns if col.endswith("_5x5")]
        ].copy()

for ds in dataset:
    if ds["name"] == "pregnant_max":
        ds["ds"] = pregnant_df[
            [col for col in pregnant_df.columns if col.endswith("_max")]
        ].copy()

for ds in dataset:
    if ds["name"] == "pregnant_3x3":
        ds["ds"] = pregnant_df[
            [col for col in pregnant_df.columns if col.endswith("_3x3")]
        ].copy()

for i in dataset:
    # print(i['name'])
    pass

ptt = paired_t_test(df=dataset[-1]['ds'], threshold=0.05, dataset_name=dataset[-1]['name'])
# ptt

pcc =pairwise_correlation(df=dataset[-1]['ds'], dataset_name=dataset[-1]['name'], meth='spearman')
# pcc

pcc =pairwise_correlation(df=dataset[-1]['ds'], dataset_name=dataset[-1]['name'], meth='pearson')
# pcc

anova = rm_anova_analysis(dataset[-1]['ds'], dataset_name=dataset[-1]['name'])
# anova

one_way_anova = oneway_anova_analysis(dataset[-1]['ds'], dataset_name=dataset[-1]['name'])
# one_way_anova

target = "rectal_" + dataset[0]['name'].split('_')[-1]
lra = linear_regression_analysis(dataset[0]['ds'],target_column=target, dataset_name=dataset[0]['name'])
lra

from shapiro_wilk import *
sw = shapiro_test(dataset[-1]["ds"], dataset_name=dataset[-1]['name'])
sw

from qq_plt import qq_plot
if qq_plot in sys.modules:
    del sys.modules[qq_plot]
    import sys
    
qq_plot(dataset[-1]["ds"], dataset_name=dataset[-1]['name'])



df_name = None
df = pd.DataFrame()
final_df = pd.DataFrame()
separater_df = pd.DataFrame(columns=["blank"])
for i in range(len(dataset)):
    df = dataset[i]["ds"]
    df_name = dataset[i]["name"]
    # correlation
    correlation_df = pairwise_correlation(df=df, dataset_name=df_name)
    # paired_t_test
    ptt = paired_t_test(df=df, dataset_name=df_name)
    # rm_anova
    rm_anova = rm_anova_analysis(df=df, dataset_name=df_name)
    # lra
    target = "rectal_" + dataset[i]["name"].split("_")[-1]
    lra = linear_regression_analysis(df=df, target_column=target, dataset_name=df_name)
    shapiro_wilki = shapiro_test(df=df, dataset_name=df_name)
    # if i == 2:
    df3 = pd.concat(
        [
            
            correlation_df,
            separater_df,
            ptt,
            separater_df,
            rm_anova,
            separater_df,
            lra,
            separater_df,
            shapiro_wilki,
            separater_df,
        ],
        axis=1,
    )
    # print(df3)
    df3.columns = [
        f"{col}_{i}" if df3.columns.duplicated()[i] else col
        for i, col in enumerate(df3.columns)
    ]

    # Create an empty row DataFrame with the same columns
    empty_row = pd.DataFrame({col: [None] for col in df3.columns})
    # empty_row = pd.concat([empty_row, empt])
    # Concatenate DataFrames with the empty row in between
    # result = pd.concat([df1, empty_row, df2], ignore_index=True)

    final_df = pd.concat(
        [final_df, empty_row, df3],
        axis=0,
    )

# final_df[final_df.select_dtypes(include="number").columns] = \
    # final_df.select_dtypes(include="number").round(2)


# final_df.dtypes

final_df.to_csv("/tmp/temperature_analysis_summary_all_groups.csv")
dff = pd.read_csv("/tmp/temperature_analysis_summary_all_groups.csv")
dff = dff.round(2)
dff.to_csv("temperature_analysis_summary_all_groups.csv")