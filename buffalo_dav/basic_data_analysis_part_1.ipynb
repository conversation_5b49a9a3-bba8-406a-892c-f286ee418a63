import pandas as pd
from scipy.stats import ttest_rel

df = pd.read_csv("buffalo_1_3_5_max_values_ver-1.csv")

df = df.drop(columns=["processed_datetime", "animal_id"])
df_cols = df.columns
df

# calves_df = pd.DataFrame(columns=df_cols)
# lactating_df = pd.DataFrame(columns=df_cols)
# pregnant_df = pd.DataFrame(columns=df_cols)

calves_df = df[df['category'] =='Calf'].copy()
lactating_df = df[df['category'] =='Lactating'].copy()
pregnant_df = df[df['category'] =='Pregnant'].copy()


if "category" in calves_df.columns:
	calves_df = calves_df.drop(columns=["category"])
# print(calves_df.mean())
# print(calves_df.std())
calves_df = calves_df.fillna(calves_df.mean()).round(decimals=2)
# print(calves_df.mean())
# print(calves_df.std())
calves_df


# 1. Body parts you're comparing
parts = ["head", "eye", "thorax", "abdomen", "rectal"]

# 2. Windows to compare within
suffixes = ["max", "3x3", "5x5"]

# 3. Store results here
results = []

calves_max = calves_df[[col for col in calves_df.columns if col.endswith("_max")]].copy()
calves_3x3 = calves_df[[col for col in calves_df.columns if col.endswith("_3x3")]].copy()
calves_5x5 = calves_df[[col for col in calves_df.columns if col.endswith("_5x5")]].copy()

calves_3x3

from paired_t_test import paired_t_test
ptt = paired_t_test(df=calves_max, threshold=0.05)
ptt

import matplotlib.pyplot as plt
import seaborn as sns

# Calculate means and stds
means = calves_3x3.mean()
stds = calves_3x3.std()

# Create bar plot with error bars
plt.figure(figsize=(8, 5))
sns.barplot(x=means.index, y=means.values, yerr=stds.values, capsize=0.2, palette='pastel')

# Add details
plt.title('Mean Temperature by Body Part (± Std Dev)')
plt.ylabel('Temperature (°C)')
plt.xlabel('Body Part')
plt.ylim(min(means.values - stds.values) - 0.5, max(means.values + stds.values) + 0.5)
plt.xticks(rotation=45)
plt.grid(axis='y', linestyle='--', alpha=0.7)

plt.tight_layout()
plt.show()


