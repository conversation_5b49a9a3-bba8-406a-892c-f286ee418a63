import pandas as pd
from scipy.stats import ttest_rel

df = pd.read_csv("buffalo_1_3_5_max_values_ver-1.csv")

df = df.drop(columns=["processed_datetime", "animal_id"])
df_cols = df.columns
df

calves_df = df[df['category'] =='Calf'].copy()
lactating_df = df[df['category'] =='Lactating'].copy()
pregnant_df = df[df['category'] =='Pregnant'].copy()

if "category" in calves_df.columns:
	calves_df = calves_df.drop(columns=["category"])
if "category" in pregnant_df.columns:
	pregnant_df = pregnant_df.drop(columns=["category"])
if "category" in lactating_df.columns:
	lactating_df = lactating_df.drop(columns=["category"])


# print(calves_df.mean())
# print(calves_df.std())
calves_df = calves_df.fillna(calves_df.mean()).round(decimals=2)
pregnant_df = pregnant_df.fillna(pregnant_df.mean()).round(decimals=2)
lactating_df = lactating_df.fillna(lactating_df.mean()).round(decimals=2)
# print(calves_df.mean())
# print(calves_df.std())
calves_df.describe()
pregnant_df.describe()
lactating_df.describe()

calves_max = calves_df[
    [col for col in calves_df.columns if col.endswith("_max")]
].copy()
calves_max

category = ["calves", "pregnant", "lactating"]
suffixes = ["max", "3x3", "5x5"]

dataset_names = []
for part in category:
    for suffix in suffixes:
        dataset_name = str(part) + "_" + str(suffix)
        dataset_names.append(dataset_name)

dataset = []
dataset.clear()
import pandas as pd

for i in range(len(dataset_names)):
    df_pair = {"name": None, "ds": None}
    df_pair["name"] = dataset_names[i]
    # dataset[dataset_names[i]] = df_pair
    dataset.append(df_pair)
# for ds in dataset:
    # print(ds)
print(dataset)

# -------------------------------------------BRUTE FORCE APPROACH-------------------------------------------
for ds in dataset:
    if ds["name"] == "calves_max":
        ds["ds"] = calves_df[
            [col for col in calves_df.columns if col.endswith("_max")]
        ].copy()

for ds in dataset:
    if ds["name"] == "calves_3x3":
        ds["ds"] = calves_df[
            [col for col in calves_df.columns if col.endswith("_3x3")]
        ].copy()
# calves_5x5 = calves_df[[col for col in calves_df.columns if col.endswith("_5x5")]].copy()
for ds in dataset:
    if ds["name"] == "calves_5x5":
        ds["ds"] = calves_df[
            [col for col in calves_df.columns if col.endswith("_5x5")]
        ].copy()

for ds in dataset:
    if ds["name"] == "lactating_5x5":
        ds["ds"] = lactating_df[
            [col for col in lactating_df.columns if col.endswith("_5x5")]
        ].copy()

for ds in dataset:
    if ds["name"] == "lactating_3x3":
        ds["ds"] = lactating_df[
            [col for col in lactating_df.columns if col.endswith("_3x3")]
        ].copy()

for ds in dataset:
    if ds["name"] == "lactating_max":
        ds["ds"] = lactating_df[
            [col for col in lactating_df.columns if col.endswith("_max")]
        ].copy()

for ds in dataset:
    if ds["name"] == "pregnant_5x5":
        ds["ds"] = pregnant_df[
            [col for col in pregnant_df.columns if col.endswith("_5x5")]
        ].copy()

for ds in dataset:
    if ds["name"] == "pregnant_max":
        ds["ds"] = pregnant_df[
            [col for col in pregnant_df.columns if col.endswith("_max")]
        ].copy()

for ds in dataset:
    if ds["name"] == "pregnant_3x3":
        ds["ds"] = pregnant_df[
            [col for col in pregnant_df.columns if col.endswith("_3x3")]
        ].copy()
for ds in dataset:
    print(ds)

# calves_3x3

from paired_t_test import paired_t_test
ptt = paired_t_test(df=dataset[0]['ds'], threshold=0.05, dataset_name=dataset[0]['name'])
ptt

from pairwise_correlation import *

pcc =pairwise_correlation(df=dataset[0]['ds'], dataset_name=dataset[0]['name'])
pcc

from anova_analysis import *
anova = rm_anova_analysis(dataset[0]['ds'], dataset_name=dataset[0]['name'])
anova

from oneway_anova import *
one_way_anova = oneway_anova_analysis(dataset[0]['ds'], dataset_name=dataset[0]['name'])
one_way_anova

