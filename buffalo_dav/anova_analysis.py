import pandas as pd
import pingouin as pg


def anova_analysis(df, dataset_name=None):
    """
    Perform repeated measures ANOVA analysis on all numeric columns in a DataFrame.

    Parameters:
    -----------
    df : pandas.DataFrame
        Input DataFrame with numeric columns
    dataset_name : str, optional
        Name of the dataset to display at the top of results

    Returns:
    --------
    pandas.DataFrame
        DataFrame with columns: Variables, F_statistic, P_value, Significant
        If dataset_name provided, adds a row at the top with the dataset name
    """

    # Get numeric columns
    columns = df.select_dtypes(include=['number']).columns.tolist()

    if len(columns) < 2:
        raise ValueError("Need at least 2 numeric columns for RM-ANOVA analysis")

    # Reshape data for repeated measures ANOVA
    df_clean = df[columns].dropna()
    df_melted = df_clean.reset_index().melt(id_vars='index', var_name='Variable', value_name='Value')
    df_melted.rename(columns={'index': 'Subject'}, inplace=True)

    # Perform repeated measures ANOVA
    rm_anova_result = pg.rm_anova(data=df_melted, dv='Value', within='Variable', subject='Subject')

    # Extract F-statistic and p-value from results
    f_statistic = rm_anova_result['F'].iloc[0]
    p_value = rm_anova_result['p-unc'].iloc[0]

    # Shorten column names for display
    short_names = []
    for col in columns:
        # Remove common suffixes and shorten
        short_name = col.replace('_max', '').replace('_3x3', '').replace('_5x5', '')
        short_names.append(short_name)

    # Create results
    results = [{
        "Variables": " vs ".join(short_names),
        "F_statistic": round(f_statistic, 4),
        "P_value": round(p_value, 6),
        "Significant": p_value < 0.05
    }]
    
    # Convert to DataFrame
    results_df = pd.DataFrame(results)
    
    # Add dataset name at the top if provided
    if dataset_name:
        header_row = pd.DataFrame({
            "Variables": [f"Dataset: {dataset_name}"],
            "F_statistic": [""],
            "P_value": [""],
            "Significant": [""]
        })
        results_df = pd.concat([header_row, results_df], ignore_index=True)
    
    return results_df


# Example usage:
# anova_results = anova_analysis(calves_3x3, dataset_name="calves_3x3")
