import pandas as pd
from scipy.stats import f_oneway


def anova_analysis(df, dataset_name=None):
    """
    Perform one-way ANOVA analysis on all numeric columns in a DataFrame.
    
    Parameters:
    -----------
    df : pandas.DataFrame
        Input DataFrame with numeric columns
    dataset_name : str, optional
        Name of the dataset to display at the top of results
    
    Returns:
    --------
    pandas.DataFrame
        DataFrame with columns: Variables, F_statistic, P_value, Significant
        If dataset_name provided, adds a row at the top with the dataset name
    """
    
    # Get numeric columns
    columns = df.select_dtypes(include=['number']).columns.tolist()
    
    if len(columns) < 2:
        raise ValueError("Need at least 2 numeric columns for ANOVA analysis")
    
    # Prepare data for ANOVA (remove NaN values)
    clean_data = []
    for col in columns:
        clean_data.append(df[col].dropna().values)
    
    # Perform one-way ANOVA
    f_statistic, p_value = f_oneway(*clean_data)
    
    # Create results
    results = [{
        "Variables": " vs ".join(columns),
        "F_statistic": round(f_statistic, 4),
        "P_value": round(p_value, 6),
        "Significant": p_value < 0.05
    }]
    
    # Convert to DataFrame
    results_df = pd.DataFrame(results)
    
    # Add dataset name at the top if provided
    if dataset_name:
        header_row = pd.DataFrame({
            "Variables": [f"Dataset: {dataset_name}"],
            "F_statistic": [""],
            "P_value": [""],
            "Significant": [""]
        })
        results_df = pd.concat([header_row, results_df], ignore_index=True)
    
    return results_df


# Example usage:
# anova_results = anova_analysis(calves_3x3, dataset_name="calves_3x3")
