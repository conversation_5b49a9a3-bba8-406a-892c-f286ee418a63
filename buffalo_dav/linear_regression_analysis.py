import pandas as pd
import numpy as np
from sklearn.linear_model import LinearRegression
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error


def linear_regression_analysis(df, target_column, dataset_name=None):
    """
    Perform linear regression analysis to find which predictor best predicts the target.
    
    Parameters:
    -----------
    df : pandas.DataFrame
        Input DataFrame with numeric columns
    target_column : str
        Name of the target column (e.g., 'rectal_3x3')
    dataset_name : str, optional
        Name of the dataset to display at the top of results
    
    Returns:
    --------
    pandas.DataFrame
        DataFrame with columns: Predictor, R_squared, RMSE, MAE, Slope, Intercept
        Sorted by R_squared (descending)
        If dataset_name provided, adds a row at the top with the dataset name
    """
    
    # Get numeric columns excluding the target
    all_columns = df.select_dtypes(include=['number']).columns.tolist()
    predictor_columns = [col for col in all_columns if col != target_column]
    
    if target_column not in all_columns:
        raise ValueError(f"Target column '{target_column}' not found in DataFrame")
    
    if len(predictor_columns) < 1:
        raise ValueError("Need at least 1 predictor column for regression analysis")
    
    results = []
    
    # Test each predictor against the target
    for predictor in predictor_columns:
        # Remove rows with NaN in either predictor or target
        clean_data = df[[predictor, target_column]].dropna()
        
        if len(clean_data) < 3:  # Need at least 3 points for regression
            continue
            
        X = clean_data[predictor].values.reshape(-1, 1)
        y = clean_data[target_column].values
        
        # Fit linear regression
        model = LinearRegression()
        model.fit(X, y)
        
        # Make predictions
        y_pred = model.predict(X)
        
        # Calculate metrics
        r2 = r2_score(y, y_pred)
        rmse = np.sqrt(mean_squared_error(y, y_pred))
        mae = mean_absolute_error(y, y_pred)
        slope = model.coef_[0]
        intercept = model.intercept_
        
        # Shorten predictor name for display
        short_predictor = predictor.replace('_max', '').replace('_3x3', '').replace('_5x5', '')
        
        results.append({
            'Predictor': short_predictor,
            'R_squared': round(r2, 4),
            'RMSE': round(rmse, 4),
            'MAE': round(mae, 4),
            'Slope': round(slope, 4),
            'Intercept': round(intercept, 4),
            'N_samples': len(clean_data)
        })
    
    # Convert to DataFrame and sort by R_squared (descending)
    results_df = pd.DataFrame(results)
    results_df = results_df.sort_values('R_squared', ascending=False).reset_index(drop=True)
    
    # Add dataset name at the top if provided
    if dataset_name:
        header_row = pd.DataFrame({
            'Predictor': [f'Dataset: {dataset_name}'],
            'R_squared': [''],
            'RMSE': [''],
            'MAE': [''],
            'Slope': [''],
            'Intercept': [''],
            'N_samples': ['']
        })
        results_df = pd.concat([header_row, results_df], ignore_index=True)
    
    return results_df


# Example usage:
# regression_results = linear_regression_analysis(calves_3x3, target_column='rectal_3x3', dataset_name="calves_3x3")
